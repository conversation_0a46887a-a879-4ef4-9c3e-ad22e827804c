<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { serverApi } from '../services/serverApi';
import MonacoEditor from '../components/MonacoEditor.vue';
import { useServerStore } from '../stores/serverStorage';

const serverText = ref<string>('');
const loading = ref(false);
const showDiff = ref(false);
const originalText = ref('');

const store = useServerStore();

const fetchServers = async (force:boolean) => {
    try {
        loading.value = true;
        await store.fetchServers(force);
        const formattedData = JSON.stringify(store.servers, null, 2);
        serverText.value = formattedData;
        originalText.value = formattedData;
    } catch (error) {
        MessagePlugin.error('获取服务器配置失败');
    } finally {
        loading.value = false;
    }
};

const handleSave = async () => {
    loading.value = true;
    try {
        if (serverText.value === '') {
            MessagePlugin.error('服务器配置不能为空');
            return;
        }
        if (originalText.value === serverText.value) {
            MessagePlugin.success('数据未修改');
            return;
        }
        const servers = JSON.parse(serverText.value);
        const result = await serverApi.update(servers);
        if (result.error) {
            MessagePlugin.error(`${result.error}`);
            return;
        }
        store.updateServers(servers || []);
        MessagePlugin.success('保存成功');
    } catch (error) {
        if (error instanceof SyntaxError) {
            MessagePlugin.error('JSON格式错误');
        } else {
            MessagePlugin.error('保存失败');
        }
    } finally {
        loading.value = false;
    }
};

const handleReload = async () => {
    loading.value = true;
    try {
        await fetchServers(true);
        MessagePlugin.success('重新加载成功');
    } catch (error) {
        MessagePlugin.error('重新加载失败');
    } finally {
        loading.value = false;
    }
};

const handleKeyDown = (e: KeyboardEvent) => {
    if ((e.ctrlKey || e.metaKey) && e.key.toLowerCase() === 's') {
        e.preventDefault();
        handleSave();
    }
};

const toggleDiff = () => {
    showDiff.value = !showDiff.value;
    if (showDiff.value && originalText.value === '') {
        originalText.value = serverText.value;
    }
};

watch(showDiff, (newVal) => {
    if (newVal && originalText.value === serverText.value) {
        MessagePlugin.info('当前没有差异可以显示');
    }
}, { immediate: true });

onMounted(async () => {
    window.addEventListener('keydown', handleKeyDown);
    await fetchServers(false);
});

onUnmounted(() => {
    window.removeEventListener('keydown', handleKeyDown);
});
</script>

<template>
    <div class="server-container h-full p-2 md:p-5">
        <t-card bordered class="h-full">
            <template #content>
                <div class="flex flex-col h-full">
                    <div class="flex justify-end items-center mb-4 gap-4">
                        <t-button variant="outline" @click="handleReload">
                            重新加载
                        </t-button>
                        <t-button variant="outline" @click="toggleDiff">
                            {{ showDiff ? '隐藏对比' : '显示对比' }}
                        </t-button>
                        <t-button theme="primary" @click="handleSave" :loading="loading">
                            保存配置
                        </t-button>
                    </div>

                    <div class="editor-container flex-1">
                        <MonacoEditor v-model:value="serverText" :original-value="showDiff ? originalText : undefined"
                            language="json" :options="{ tabSize: 2 }" />
                    </div>
                </div>
            </template>
        </t-card>
    </div>
</template>

<style scoped>
.server-container {
    width: 100%;
}

:deep(.t-card__body) {
    height: 100%;
}

.editor-container {
    border: 1px solid var(--td-component-border);
}
</style>
