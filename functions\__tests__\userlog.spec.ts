import fs from 'fs';
import moment from 'moment-timezone';
import path from 'path';
import { describe, expect, it } from 'vitest';

describe('User Log Tests', () => {
  const userLogPath = path.join(__dirname, '../../logs/user.log');
  
  it('should read user.log file', () => {
    const content = fs.readFileSync(userLogPath, 'utf-8');
    expect(content).toBeDefined();
    expect(content.length).toBeGreaterThan(0);
  });

  it('should parse log entries correctly', () => {
    const content = fs.readFileSync(userLogPath, 'utf-8');
    const lines = content.split('\n').filter(line => line.trim());
    
    lines.forEach(line => {
      expect(line).toMatch(/^\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2}\.\d{3}\sinfo:/);
      expect(line).toMatch(/balance: \d+$/);
    });
  });

  it('should send log data to API', async () => {
    const content = fs.readFileSync(userLogPath, 'utf-8');
  
    const datetime = moment().tz('Asia/Shanghai').format('YYYY-MM-DD');
    const response = await fetch('http://localhost:8788/log/post', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer 9ba4c11ce3a9235a34b3481795485dec'
      },
      body: JSON.stringify({
        server: 'hwsh1',
        service: 'msr-user',
        datetime: datetime,
        log: content
      })
    });
    expect(response.status).toBe(200);
    const data:any = await response.json();
    expect(data.success).toBe(true);
  });
});
