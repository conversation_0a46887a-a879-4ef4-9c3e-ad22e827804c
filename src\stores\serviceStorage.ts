import { defineStore } from 'pinia'
import { logApi } from '../services/logApi'

export const useServiceStore = defineStore('service', {
  state: () => ({
    services: [] as any[],
    initialized: false
  }),
  
  actions: {
    async fetchServices(force = false) {
      if (!force && this.initialized) return;
      try {
        const data = await logApi.listServices()
        this.services = data || []
        this.initialized = true
      } catch (error) {
        console.error('Failed to fetch services:', error)
      }
    },
    
    updateServices(newServices: any[]) {
      this.services = newServices
    }
  }
})
