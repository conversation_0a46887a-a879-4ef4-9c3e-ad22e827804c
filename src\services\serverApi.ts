
import { API_BASE_URL, getHeaders, handleResponse } from './util';

export interface ServerConfig {
    id: string;
    name: string;
    disabled:boolean;
}

export const serverApi = {
    async update( servers: any) {
        const response = await fetch(
            `${API_BASE_URL}/server/post`,
            {
                headers: getHeaders(),
                method: 'POST',
                body: JSON.stringify(servers)
            }
        );
        return handleResponse(response);
    },

    async get(): Promise<any> {
        const response = await fetch(
            `${API_BASE_URL}/server/get`,
            {
                headers: getHeaders()
            }
        );
        return handleResponse(response);
    },
}
