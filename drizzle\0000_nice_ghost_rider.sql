CREATE TABLE `accounts` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`email` text NOT NULL,
	`password` text NOT NULL,
	`proofEmail` text,
	`server` text DEFAULT '' NOT NULL,
	`score` integer DEFAULT 0 NOT NULL,
	`weight` integer DEFAULT 0 NOT NULL,
	`disabled` integer DEFAULT 0 NOT NULL,
	`lock` integer DEFAULT 0 NOT NULL
);
--> statement-breakpoint
CREATE INDEX `server_idx` ON `accounts` (`server`);--> statement-breakpoint
CREATE TABLE `logs` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`server` text NOT NULL,
	`service` text NOT NULL,
	`datetime` text NOT NULL,
	`logs` text NOT NULL
);
--> statement-breakpoint
CREATE INDEX `logs_compound_idx` ON `logs` (`server`,`service`,`datetime`);--> statement-breakpoint
CREATE TABLE `settings` (
	`key` text PRIMARY KEY NOT NULL,
	`value` text NOT NULL
);
