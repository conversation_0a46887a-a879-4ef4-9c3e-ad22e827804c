import { EventContext } from '@cloudflare/workers-types'
import { Env } from '../types'
import { authMiddleware } from '../utils/auth';
import { handleOptions, addCorsHeaders } from '../utils/cors';
import { drizzle } from 'drizzle-orm/d1';
import { accountsTable } from '../db/schema';
import { eq } from 'drizzle-orm';

export const onRequest = async (context: EventContext<Env, string, Record<string, unknown>>): Promise<Response> => {
    const request = context.request;
    const env = context.env as Env;
    const db = drizzle(env.DB);

    // 验证权限
    const authResponse = await authMiddleware(request, env);
    if (authResponse) {
        return addCorsHeaders(authResponse);
    }

    try {
        const url = new URL(request.url);
        const lock = parseInt(url.searchParams.get('lock') || "0");
        const lockedAccounts = await db.select()
            .from(accountsTable)
            .where(eq(accountsTable.lock, lock));

        return addCorsHeaders(new Response(JSON.stringify(lockedAccounts), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
        }));
    } catch (error) {
        return addCorsHeaders(new Response(JSON.stringify({
            error: error instanceof Error ? error.message : 'Failed to retrieve locked accounts'
        }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        }));
    }
}
