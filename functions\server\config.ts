import { EventContext } from '@cloudflare/workers-types'
import { Env } from '../types'
import { authApiToken, authMiddleware } from '../utils/auth';
import { handleOptions, addCorsHeaders } from '../utils/cors';
import { drizzle } from 'drizzle-orm/d1';
import { serversTable } from '../db/schema';
import { eq } from 'drizzle-orm';

export const onRequest = async (context: EventContext<Env, string, Record<string, unknown>>): Promise<Response> => {
    const request = context.request;
    const env = context.env as Env;
    const db = drizzle(env.DB);

    // Handle OPTIONS request
    if (request.method === 'OPTIONS') {
        return handleOptions();
    }

    // 验证权限
    const authResponse = await authMiddleware(request, env);
    const apiResponse = await authApiToken(request, env);
    if (authResponse && apiResponse) {
        return addCorsHeaders(authResponse);
    }

    try {
        // Get server ID from URL
        const url = new URL(request.url);
        const id = url.searchParams.get('id');
        
        if (!id) {
            throw new Error('Server ID is required');
        }

        // Get server config
        const server = await db.select({
            config: serversTable.config
        })
        .from(serversTable)
        .where(eq(serversTable.id, id))
        .get();

        if (!server) {
            return addCorsHeaders(new Response(JSON.stringify({
                error: 'Server not found'
            }), {
                status: 404,
                headers: { 'Content-Type': 'application/json' }
            }));
        }

        // Parse JSON config
        let config;
        try {
            config = JSON.parse(server.config || '{}');
        } catch (e) {
            config = {};
        }

        return addCorsHeaders(new Response(JSON.stringify(config), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
        }));

    } catch (error) {
        return addCorsHeaders(new Response(JSON.stringify({
            error: error instanceof Error ? error.message : 'Failed to retrieve server config'
        }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        }));
    }
}
