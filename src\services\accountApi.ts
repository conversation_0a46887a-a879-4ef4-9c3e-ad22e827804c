import { API_BASE_URL, getHeaders, handleResponse } from './util';

export const accountApi = {
    async update(server: string, accounts: any) {
        const response = await fetch(
            `${API_BASE_URL}/account/post?server=${server}`,
            {
                headers: getHeaders(),
                method: 'POST',
                body: JSON.stringify(accounts)
            }
        );
        return handleResponse(response);
    },

    async get(server: string): Promise<any> {
        const response = await fetch(
            `${API_BASE_URL}/account/get?server=${server}&ignoreDisabled=1`,
            {
                headers: getHeaders()
            }
        );
        return handleResponse(response);
    },
    async locked(state:number): Promise<any> {
        const response = await fetch(
            `${API_BASE_URL}/account/locked?lock=${state}`,
            {
                headers: getHeaders()
            }
        );
        return handleResponse(response);
    },

    async delete(emails: string[]): Promise<any> {
        const response = await fetch(
            `${API_BASE_URL}/account/delete`, // 使用 delete 端点
            {
                headers: getHeaders(),
                method: 'DELETE', // 使用 DELETE 方法
                body: JSON.stringify(emails) // 发送 email 数组
            }
        );
        return handleResponse(response);
    }
}
