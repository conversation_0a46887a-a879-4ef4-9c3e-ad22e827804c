import { EventContext } from '@cloudflare/workers-types'
import { Env } from '../types'
import { authMiddleware } from '../utils/auth';
import {  addCorsHeaders } from '../utils/cors';
import { type Log } from './logServices';
import { drizzle } from 'drizzle-orm/d1';
import { logsTable } from '../db/schema';
import { eq, and } from 'drizzle-orm';

export const onRequest = async (context: EventContext<Env, string, Record<string, unknown>>): Promise<Response> => {
    const request = context.request;
    const env = context.env as Env;
    const db = drizzle(env.DB);
    // 验证权限
    const authResponse = await authMiddleware(request, env);
    if (authResponse) {
        return addCorsHeaders(authResponse);
    }

    const url = new URL(request.url);
    const server = url.searchParams.get('server');
    const service = url.searchParams.get('service');
    const datetime = url.searchParams.get('datetime');

    if (!server || !service || !datetime) {
        return new Response('Missing parameters', { status: 400 });
    }

    // 获取日志数据
    const result = await db.select()
        .from(logsTable)
        .where(
            and(
                eq(logsTable.server, server),
                eq(logsTable.service, service),
                eq(logsTable.datetime, datetime)
            )
        )
        .get();

    if (!result) {
        return new Response(JSON.stringify({ logs: [], total: 0 }), {
            headers: { 'Content-Type': 'application/json' }
        });
    }

    const logs = JSON.parse(result.logs) as Log[];

    return new Response(JSON.stringify({
        logs: logs,
        total: logs.length
    }), {
        headers: { 'Content-Type': 'application/json' }
    });
}