import { API_BASE_URL, getHeaders, handleResponse } from './util';
import type { SystemSettings } from '../../functions/types';

export const settingApi = {
    async update(settings: SystemSettings) {
        const response = await fetch(
            `${API_BASE_URL}/setting/post`,
            {
                headers: getHeaders(),
                method: 'POST',
                body: JSON.stringify(settings)
            }
        );
        return handleResponse(response);
    },

    async get(): Promise<SystemSettings> {
        const response = await fetch(
            `${API_BASE_URL}/setting/get`,
            {
                headers: getHeaders()
            }
        );
        return handleResponse(response);
    },
}