import fs from 'fs';
import moment from 'moment-timezone';
import path from 'path';
import { describe, expect, it } from 'vitest';

describe('Error Log Tests', () => {
  const errorLogPath = path.join(__dirname, '../../logs/error.log');
  
  it('should read error.log file', () => {
    const content = fs.readFileSync(errorLogPath, 'utf-8');
    expect(content).toBeDefined();
    expect(content.length).toBeGreaterThan(0);
  });

  it('should parse error entries correctly', () => {
    const content = fs.readFileSync(errorLogPath, 'utf-8');
    const lines = content.split('\n').filter(line => line.trim());
    
    lines.forEach(line => {
      expect(line).toMatch(/^\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2}\.\d{3}\serror:/);
    });
  });

  it('should categorize errors correctly', () => {
    const content = fs.readFileSync(errorLogPath, 'utf-8');
    const lines = content.split('\n').filter(line => line.trim());
    
    const pcErrors = lines.filter(line => line.includes('PC错误'));
    const mobileErrors = lines.filter(line => line.includes('移动端错误'));
    
    expect(pcErrors.length).toBeGreaterThan(0);
    expect(mobileErrors.length).toBeGreaterThan(0);
  });

  it('should send error log to API', async () => {
    const content = fs.readFileSync(errorLogPath, 'utf-8');
 
    const datetime = moment().tz('Asia/Shanghai').format('YYYY-MM-DD');
    const response = await fetch('http://localhost:8788/log/post', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer 9ba4c11ce3a9235a34b3481795485dec'
      },
      body: JSON.stringify({
        server: 'hwsh1',
        service: 'msr-error',
        datetime: datetime,
        log: content
      })
    });

    expect(response.status).toBe(200);
    const data:any = await response.json();
    expect(data.success).toBe(true);
  });
});
