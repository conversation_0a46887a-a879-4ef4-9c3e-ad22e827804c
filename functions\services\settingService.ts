import { drizzle } from 'drizzle-orm/d1';
import { settingsTable } from '../db/schema';
import { Env, SystemSettings } from '../types';

export async function getSystemSettings(env: Env): Promise<SystemSettings> {
    const db = drizzle(env.DB);
    const settings = await db.select().from(settingsTable).all();

    if (settings.length === 0) {
        return {
            feishu: {
                app_id: "",
                app_secret: "",
                verification_token: "",
                encrypt_key: "",
                receive_id: ""
            }
        };
    }

    // 将数据库记录转换为对象
    return settings.reduce((acc, curr) => {
        try {
            acc[curr.key] = JSON.parse(curr.value);
        } catch (e) {
            acc[curr.key] = curr.value;
        }
        return acc;
    }, {} as any);
}
