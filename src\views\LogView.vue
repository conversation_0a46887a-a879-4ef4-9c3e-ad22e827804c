<script setup lang="ts">
import { computed, ref, watch, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { logApi } from '../services/logApi';
import { MessagePlugin } from 'tdesign-vue-next';
import moment from 'moment-timezone';
import { useServerStore } from '../stores/serverStorage';
import { useServiceStore } from '../stores/serviceStorage';
const serverStore = useServerStore();
const serviceStore = useServiceStore();
const loading = ref(false);
const selectedServerId = ref<string>('');
const selectedServiceId = ref<string>('');
const datetime = ref<string>(moment().tz('Asia/Shanghai').format('YYYY-MM-DD'));
const pagination = ref({
    current: 1,
    total: 0,
    pageSize: 18
});

const showDetailDialog = ref(false);
const selectedLogContent = ref('');

const sort = ref<{ sortBy: string; descending: boolean }>({
    sortBy: 'datetime',
    descending: true
});

const columns = [
    { colKey: 'datetime', title: '时间', width: 150, sorter: true, ellipsis: true },
    { colKey: 'user', title: '用户', width: 150, sorter: true, ellipsis: true },
    {
        colKey: 'content',
        title: '内容',
        ellipsis: true,
        sorter: true
    }
];

const handleRowClick = (row: any) => {
    selectedLogContent.value = row.row.content;
    showDetailDialog.value = true;
};

const isInteger = (str: string) => {
    const num = Number(str.split('\n')[0].trim());
    return !isNaN(num) && Number.isInteger(num);
};

const compareContent = (a: string, b: string) => {
    const firstLineA = a.split('\n')[0].split("|")[0].trim();
    const firstLineB = b.split('\n')[0].split("|")[0].trim();

    if (isInteger(firstLineA) && isInteger(firstLineB)) {
        return Number(firstLineA) - Number(firstLineB);
    }
    return firstLineA.localeCompare(firstLineB);
};

const allLogs = ref<any[]>([]);
const userSearch = ref(''); // 添加用户搜索字段

// 使用计算属性处理排序和分页
const processedLogs = computed(() => {
    let result = [...allLogs.value];

    // 处理用户服务的日志内容
    if (selectedServiceId.value === 'msr-user') {
        result = result.map(log => ({
            ...log,
            content: log.content.replace('用户信息:', '')
        }));
    }

    // 用户搜索过滤
    if (userSearch.value) {
        result = result.filter(log =>
            log.user.toLowerCase().includes(userSearch.value.toLowerCase())
        );
    }

    // 排序处理
    if (sort.value.sortBy) {
        result.sort((a, b) => {
            const factor = sort.value.descending ? -1 : 1;
            if (sort.value.sortBy === 'content' && selectedServiceId.value === 'msr-user') {
                return factor * compareContent(a.content, b.content);
            }
            return factor * (a[sort.value.sortBy] > b[sort.value.sortBy] ? 1 : -1);
        });
    }

    pagination.value.total = result.length; // 更新总数以反映过滤后的结果

    // 分页处理
    const start = (pagination.value.current - 1) * pagination.value.pageSize;
    const end = start + pagination.value.pageSize;
    return result.slice(start, end);
});

const handleSort = (sortInfo: { sortBy: string; descending: boolean }) => {
    sort.value = sortInfo;
    pagination.value.current = 1;
};

const fetchLogs = async () => {
    loading.value = true;
    try {
        const result = await logApi.listLog(
            selectedServerId.value,
            selectedServiceId.value,
            datetime.value
        );
        allLogs.value = result.logs;
        pagination.value.total = result.total;
    } catch (error) {
        MessagePlugin.error('获取日志失败');
    } finally {
        loading.value = false;
    }
};

// 添加 onMounted 钩子
onMounted(async () => {
    await serverStore.fetchServers();
    await serviceStore.fetchServices();
    if (serverStore.servers.length > 0 && serviceStore.services.length > 0) {
        selectedServerId.value = serverStore.servers[0].id;
        selectedServiceId.value = serviceStore.services[0].id;
    }
});

watch([selectedServerId, selectedServiceId, datetime], async () => {
    pagination.value.current = 1;
    await fetchLogs();
});
</script>

<template>

    <div class="w-full h-full flex flex-col p-2 md:p-5 gap-2 md:gap-5">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div class="flex flex-col md:flex-row md:items-center gap-4">
                <t-select v-model="selectedServerId" class="w-full md:w-40">
                    <t-option v-for="s in serverStore.servers" :key="s.id" :value="s.id" :label="s.name" />
                </t-select>
                <t-select v-model="selectedServiceId" class="w-full md:w-40">
                    <t-option v-for="s in serviceStore.services" :key="s.id" :value="s.id" :label="s.name" />
                </t-select>
                <t-date-picker v-model="datetime" mode="date" class="w-full md:w-40" />

                <t-input v-model="userSearch" class="w-full md:w-40" placeholder="搜索用户" clearable />
            </div>
        </div>

        <div class="overflow-x-auto flex-1 overflow-y-auto">
            <t-table :data="processedLogs" :loading="loading" :columns="columns" row-key="datetime" hover
                @row-click="handleRowClick" :sort="sort" @sort-change="handleSort" size="small" class="min-w-full" />
        </div>

        <div class="flex justify-end ">
            <t-pagination v-model="pagination.current" :total="pagination.total" :page-size="pagination.pageSize"
                size="small" />
        </div>

        <t-dialog v-model:visible="showDetailDialog" header="日志详情" :footer="false" class="w-11/12 md:w-2/3 max-w-3xl">
            <div class="whitespace-pre-wrap max-h-[70vh] overflow-y-auto text-sm">{{ selectedLogContent }}</div>
        </t-dialog>
    </div>

</template>

<style scoped>
@media (max-width: 768px) {
    :deep(.t-table__header) {
        font-size: 14px;
    }

    :deep(.t-table td) {
        font-size: 13px;
    }
}
</style>
