import * as dotenv from 'dotenv'
import { Config, defineConfig } from 'drizzle-kit';
dotenv.config({ path: ['.env','.env.local'],override: true });
//https://github.com/drizzle-team/drizzle-orm/issues/3728
//生产模式下，就不要用npx drizzle-kit push了，使用npx drizzle-kit migrate

//npx drizzle-kit generate --config=drizzle-prod.drizzle.config.ts
//npx drizzle-kit migrate --config=drizzle-prod.drizzle.config.ts

export default defineConfig({
  out: './drizzle',
  schema: './functions/db/schema.ts',
  dialect: 'sqlite',
  driver: 'd1-http',
  dbCredentials: {
    accountId: process.env.CLOUDFLARE_ACCOUNT_ID!,
    databaseId: process.env.CLOUDFLARE_DATABASE_ID!,
    token: process.env.CLOUDFLARE_D1_TOKEN!,
  },
} satisfies Config);
