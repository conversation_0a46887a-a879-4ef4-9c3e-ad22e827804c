import { EventContext } from '@cloudflare/workers-types'
import { Env, LoginCredentials, SystemSettings } from '../types'
import { generateToken } from '../utils/jwt';
import { authApiToken, authMiddleware } from '../utils/auth';
import { handleOptions, addCorsHeaders } from '../utils/cors';

import { services, type Log } from './logServices';
import { getTenantAccessToken, sendFeishuMessageText } from '../msg/utils';
import { drizzle } from 'drizzle-orm/d1';
import { accountsTable, logsTable } from '../db/schema';
import { getSystemSettings } from '../services/settingService';
import { eq, and, lt } from 'drizzle-orm';

interface LogData {
    server: string;
    service: string;
    datetime: string;
    log: string;
}

export const onRequest = async (context: EventContext<Env, string, Record<string, unknown>>): Promise<Response> => {
    const request = context.request;
    const env = context.env as Env;
    const db = drizzle(env.DB);
    // 验证权限
    const authResponse = await authApiToken(request, env);
    if (authResponse) {
        return addCorsHeaders(authResponse);
    }

    const logData = await request.json() as LogData;

    if (!logData.log) {
        return new Response(JSON.stringify({ success: true }), {
            headers: { 'Content-Type': 'application/json' }
        });
    }

    // 验证必要字段
    if (!logData.server || !logData.service || !logData.datetime) {
        return new Response('Missing required fields', { status: 400 });
    }

    // 验证服务是否存在
    const serviceConfig = services.find(s => s.id === logData.service);
    if (!serviceConfig) {
        return new Response('Invalid server or service', { status: 400 });
    }

    // 删除7天前的日志 - 修复1：使用正确的SQL条件构建方式
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    const formattedSevenDaysAgo = sevenDaysAgo.toISOString().split('T')[0]; // 转为 YYYY-MM-DD 格式
    await db.delete(logsTable)
        .where(lt(logsTable.datetime, formattedSevenDaysAgo));

    // 预处理日志
    const logLines = serviceConfig.preprocess
        ? serviceConfig.preprocess(logData.log)
        : logData.log.split('\n').filter(line => line.trim());

    // 应用过滤器
    const filteredLines = logLines.filter(line => {
        if (!serviceConfig.filter || serviceConfig.filter.length === 0) return true;
        return serviceConfig.filter.some(filter => {
            if (filter instanceof RegExp) return filter.test(line);
            return line.includes(filter);
        });
    });

    // 处理日志
    const processedLogs: Log[] = [];
    const notifications: Log[] = [];

    for (const line of filteredLines) {
        const processedResult = serviceConfig.process
            ? serviceConfig.process(line)
            : {
                user: 'system',
                content: line,
                datetime: logData.datetime,
                client: 'unknown',
                server: logData.server
            };

        if (processedResult) {
            processedLogs.push(processedResult);
        }

        // 检查是否需要通知
        if (serviceConfig.notify && serviceConfig.notify.length > 0) {
            const shouldNotify = serviceConfig.notify.some(pattern => {
                if (pattern instanceof RegExp) return pattern.test(line);
                return line.includes(pattern);
            });

            if (shouldNotify && processedResult) {
                const accounts = await db.select()
                    .from(accountsTable)
                    .where(eq(accountsTable.email, processedResult.user)).limit(1);
                //代表这个账号未处理过
                if (accounts.length == 0 || (accounts.length > 0 && accounts[0].lock === 0)) {
                    notifications.push(processedResult);
                }
            }
        }
    }

    // 应用后处理
    const finalLogs = serviceConfig.postprocess
        ? await serviceConfig.postprocess(processedLogs, env)
        : processedLogs;


    // 保存处理后的日志 - 修复2：检查当天是否已存在记录
    if (finalLogs.length > 0) {
        // 查询当天该服务器和服务的日志是否存在
        const existingLog = await db.select()
            .from(logsTable)
            .where(
                and(
                    eq(logsTable.server, logData.server),
                    eq(logsTable.service, logData.service),
                    eq(logsTable.datetime, logData.datetime)
                )
            )
            .limit(1);

        if (existingLog.length > 0) {
            // 如果存在，更新logs字段
            await db.update(logsTable)
                .set({ logs: JSON.stringify(finalLogs) })
                .where(
                    and(
                        eq(logsTable.server, logData.server),
                        eq(logsTable.service, logData.service),
                        eq(logsTable.datetime, logData.datetime)
                    )
                );
        } else {
            // 如果不存在，插入新记录
            await db.insert(logsTable).values({
                server: logData.server,
                service: logData.service,
                datetime: logData.datetime,
                logs: JSON.stringify(finalLogs)
            });
        }
    }

    const settings = await getSystemSettings(env);
    const tenantResult = await getTenantAccessToken(settings.feishu.app_id, settings.feishu.app_secret);

    // 发送通知
    if (notifications.length > 0) {
        try {
            const notificationMessage = notifications.map(notification =>
                `用户: ${notification.user}\n内容: ${notification.content}\n时间: ${notification.datetime}`
            ).join('\n\n---\n\n');
            await sendFeishuMessageText(tenantResult.tenant_access_token, settings.feishu.receive_id, `服务器: ${logData.server}\n服务: ${serviceConfig.name}\n\n---\n\n${notificationMessage}`);
        } catch (error) {
            console.error('发送通知失败:', error);
        }
    }

    return new Response(JSON.stringify({ success: true }), {
        headers: { 'Content-Type': 'application/json' }
    });
}
