import { EventContext } from '@cloudflare/workers-types'
import { Env } from '../types'
import { authMiddleware } from '../utils/auth';
import { handleOptions, addCorsHeaders } from '../utils/cors';
import { drizzle } from 'drizzle-orm/d1';
import { accountsTable } from '../db/schema';
import { eq, inArray } from 'drizzle-orm';

export const onRequest = async (context: EventContext<Env, string, Record<string, unknown>>): Promise<Response> => {
    const request = context.request;
    const env = context.env as Env;
    const db = drizzle(env.DB);

    // 处理 OPTIONS 请求 (CORS 预检)
    if (request.method === 'OPTIONS') {
        return handleOptions(); // 移除 request 参数
    }

    // 验证权限
    const authResponse = await authMiddleware(request, env);
    if (authResponse) {
        return addCorsHeaders(authResponse);
    }

    // 只允许 DELETE 方法
    if (request.method !== 'DELETE') {
        return addCorsHeaders(new Response(JSON.stringify({ error: 'Method Not Allowed' }), {
            status: 405,
            headers: { 'Content-Type': 'application/json' }
        }));
    }

    try {
        const emailsToDelete = await request.json() as string[];

        if (!Array.isArray(emailsToDelete) || emailsToDelete.some(email => typeof email !== 'string')) {
            return addCorsHeaders(new Response(JSON.stringify({
                error: 'Invalid request body. Expected an array of email strings.'
            }), {
                status: 400,
                headers: { 'Content-Type': 'application/json' }
            }));
        }

        if (emailsToDelete.length === 0) {
            return addCorsHeaders(new Response(JSON.stringify({
                message: 'No emails provided for deletion.',
                timestamp: new Date().toISOString()
            }), {
                status: 200, // 或者 204 No Content
                headers: { 'Content-Type': 'application/json' }
            }));
        }

        // 分批删除账号以避免 SQL 变量限制
        const BATCH_SIZE = 100; // 设置合理的批处理大小
        let totalDeleted = 0;

        for (let i = 0; i < emailsToDelete.length; i += BATCH_SIZE) {
            const batch = emailsToDelete.slice(i, i + BATCH_SIZE);
            if (batch.length > 0) {
                const deleteResult = await db.delete(accountsTable)
                    .where(inArray(accountsTable.email, batch));
                totalDeleted += deleteResult.meta.changes ?? 0;
            }
        }

        return addCorsHeaders(new Response(JSON.stringify({
            message: `Successfully processed deletion request. ${totalDeleted} accounts deleted.`,
            timestamp: new Date().toISOString()
        }), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
        }));

    } catch (error) {
        console.error('Error deleting accounts:', error); // 添加日志记录
        let errorMessage = 'Failed to delete accounts';
        if (error instanceof Error) {
            errorMessage = error.message;
        } else if (typeof error === 'string') {
            errorMessage = error;
        }

        return addCorsHeaders(new Response(JSON.stringify({
            error: errorMessage
        }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        }));
    }
}
