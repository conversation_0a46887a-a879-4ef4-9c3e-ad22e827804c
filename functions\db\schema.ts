import { int, sqliteTable, text, index } from "drizzle-orm/sqlite-core";


// Account table with the required fields
export const accountsTable = sqliteTable("accounts", {
  id: int().primaryKey({ autoIncrement: true }),
  email: text().notNull(),
  password: text().notNull(),
  proofEmail: text(),
  server: text().notNull().default(""),
  score: int().notNull().default(0),
  weight: int().notNull().default(0),
  disabled: int().notNull().default(0),
  lock: int().notNull().default(0),
  pcSearchPointProgress: int().notNull().default(0),
  mobileSearchPointProgress: int().notNull().default(0),
  pcSearchCount: int().notNull().default(0),
  mobileSearchCount: int().notNull().default(0),
  executions: int().notNull().default(0),
  createDatetime: text().default(""),
  updateDatetime: text().default(""),
  maxDailyExecutionLimit: int().notNull().default(-1),// 默认值是-1
  maxSearchPerRequest: int().notNull().default(-1),  // 默认值是-1
  maxDailySearchLimit: int().notNull().default(-1),  // 默认值是-1
  maxReadPerRequest: int().notNull().default(-1),    // 默认值是-1
  maxDailyReadLimit: int().notNull().default(-1),    // 默认值是-1
  ignoreDistributionCycleDays: int().notNull().default(0),
  onlyLogin: int().notNull().default(0),
}, (table) => [
  index('server_idx').on(table.server)
]);

export const settingsTable = sqliteTable("settings", {
  key: text().primaryKey(),
  value: text().notNull()
});

export const logsTable = sqliteTable("logs", {
  id: int().primaryKey({ autoIncrement: true }),
  server: text().notNull(),
  service: text().notNull(),
  datetime: text().notNull(),
  logs: text().notNull(),
}, (table) => [
  index('logs_compound_idx').on(table.server, table.service, table.datetime)
]);

export const serversTable = sqliteTable("servers", {
  id: text().primaryKey(),
  name: text().notNull(),
  disabled: int().notNull().default(0),
  config: text().default('{}'),
}, (table) => [
  index('server_name_idx').on(table.name)
]);
