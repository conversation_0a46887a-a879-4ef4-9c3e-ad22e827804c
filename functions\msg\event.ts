import { EventContext } from '@cloudflare/workers-types'
import { Env,  LoginCredentials, SystemSettings } from '../types'
import { generateToken } from '../utils/jwt';
import { authApiToken, authMiddleware } from '../utils/auth';
import { handleOptions, addCorsHeaders } from '../utils/cors';
import { AESCipher, EventMessage, getTenantAccessToken, handleAuth, sendFeishuCreateCard, sendFeishuMessageCard,  updateMarkdownCard } from './utils';

//飞书的事件订阅,飞书可以通过设置事件订阅地址,将事件推送到此处.然后使用飞书的api就可以进行消息推送等操作
//https://open.feishu.cn/document/server-docs/event-subscription-guide/overview
//点击保存后，飞书服务器会向请求地址推送一个 application/json;charset=utf-8 格式的 POST 请求，用于验证所配置的请求地址的合法性。
// 该 POST 请求中会携带一个 challenge 字段，应用需要在 1 秒内，将接收到的 challenge 值原样返回给飞书开放平台。
// 否则，平台将报“Challenge code 没有返回”的错误。
// 事件处理的很多.需要再回调地址下面进行添加
export const onRequest = async (context: EventContext<Env, string, Record<string, unknown>>): Promise<Response> => {
    const request = context.request;

    const env = context.env as Env;
    const kv = env.KV || KV;
    const settings = JSON.parse(await kv.get('settings') as string) as SystemSettings;
    const data = await handleAuth(request, settings.feishu.verification_token, settings.feishu.encrypt_key);
    if (data instanceof Response) {
        return data;
    }
    //https://open.feishu.cn/document/server-docs/im-v1/message/events/receive
    let body = data as EventMessage;
    const msg = (JSON.parse(body.event.message.content)).text;
    const messages = [
        {
            role: "user",
            content: msg,
        },
    ];
    // 立即返回响应防止重发
    const responsePromise = (async () => {
        try {
            const stream: any = await env.AI.run("@cf/meta/llama-3.3-70b-instruct-fp8-fast", {
                messages,
                stream: true,
            });
            if (body.event.message.chat_type === 'p2p') {
 
                const element_id: string = "markdown_content";
                //https://open.feishu.cn/document/uAjLw4CM/ukzMukzMukzM/feishu-cards/streaming-updates-openapi-overview#5ac65a50
                const tenantResult = await getTenantAccessToken(settings.feishu.app_id, settings.feishu.app_secret);
                const cardResponse = await sendFeishuCreateCard(tenantResult.tenant_access_token, '', "", element_id);
                const sendResponse = await sendFeishuMessageCard(tenantResult.tenant_access_token, cardResponse.data.card_id, settings.feishu.receive_id);
                let sequence = 0;
                const decoder = new TextDecoder();
                const reader = stream.getReader();
                try {
                    let fullResponse = '';
                    while (true) {
                        const { done, value } = await reader.read();
                        if (done) break;

                        // 将 Uint8Array 转换为字符串
                        const textChunk = decoder.decode(value);
                        console.log("chunk", textChunk);
                        // 方式2：实时处理每个消息块
                        const responseChunk = JSON.parse(textChunk.slice(5));
                        if (responseChunk.response) {
                            fullResponse += responseChunk.response;
                            console.log("fullResponse", fullResponse);
                            sequence++;
                            await updateMarkdownCard({
                                token: tenantResult.tenant_access_token,
                                card_id: cardResponse.data.card_id,
                                content: fullResponse,
                                sequence,
                                element_id
                            });
                        }
                    }
                } finally {
                    reader.releaseLock();
                }
            }
            else {
                //群聊https://open.feishu.cn/document/server-docs/im-v1/message/reply
            }
        } catch (error) {
            console.error('异步处理失败:', error);
            // 可添加重试逻辑或错误上报
        }
    })();
    // Cloudflare Workers 环境推荐使用
    context.waitUntil(responsePromise);
    return new Response('OK', { status: 200 });
}



