import type { Env } from '../types';
import { drizzle } from 'drizzle-orm/d1';
import { accountsTable } from '../db/schema';
import { eq, and } from 'drizzle-orm';
import { authMiddleware } from '../utils/auth';
import { addCorsHeaders } from '../utils/cors';
import moment from 'moment-timezone';
export interface ServiceConfig {
    id: string;
    name: string;
    filter: (string | RegExp)[];
    notify: (string | RegExp)[];
    preprocess?: (log: string) => string[];  // 分割日志
    process?: (line: string) => Log | undefined;       // 处理日志行
    postprocess?: (logs: Log[], env: Env) => Promise<Log[]> | Log[];  // 支持异步和同步操作
}

export interface Log {
    user: string;
    content: string;
    datetime: string;  //2025-01-28 01:13:16.190	
    client: string;
}

export const splitLogsByDate = (log: string): string[] => {
    const dateRegex = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3}/;
    const lines = log.split('\n');
    const chunks: string[] = [];
    let currentChunk: string[] = [];

    lines.forEach(line => {
        if (dateRegex.test(line)) {
            if (currentChunk.length > 0) {
                chunks.push(currentChunk.join('\n'));
            }
            currentChunk = [line];
        } else if (line.trim()) {
            currentChunk.push(line);
        }
    });

    if (currentChunk.length > 0) {
        chunks.push(currentChunk.join('\n'));
    }

    return chunks;
};

export const processErrorLog = (line: string): Log | undefined => {
    const match = line.match(/^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3}) error: ([^:]+?):([\s\S]+)$/);
    if (!match) return undefined;

    const [, datetime, user, content] = match;
    return {
        datetime,
        user,
        content: content.trim(),
        client: 'error'
    };
};

export const processUserLog = (line: string): Log | undefined => {
    // 调整正则表达式以更准确匹配日志格式
    const match = line.match(/^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3}) info: ([^:]+):([\s\S]+)$/);
    if (!match) return undefined;

    const [, datetime, user, content] = match;
    return {
        datetime,
        user,
        content: content.trim(),
        client: 'user'
    };
};


export const services: ServiceConfig[] = [
    {
        id: 'msr-error',
        name: '错误日志',
        filter: [],
        notify: [],
        preprocess: splitLogsByDate,
        process: processErrorLog,
        postprocess: (logs) => logs.filter(log => log !== undefined)  // 同步操作
    },
    {
        id: 'msr-user',
        name: '用户日志',
        filter: [],
        notify: ["账号已被锁定"],
        preprocess: splitLogsByDate,
        process: processUserLog,
        postprocess: async (logs, env) => {
            // 首先过滤掉 undefined
            const validLogs = logs.filter(log => log !== undefined);

            // 使用 Map 统计执行次数并保留最新记录
            const userExecutions = new Map<string, number>();
            const latestLogsByUser = new Map<string, Log>();
            validLogs.forEach(log => {
                userExecutions.set(log.user, (userExecutions.get(log.user) || 0) + 1);
                const existingLog = latestLogsByUser.get(log.user);
                if (!existingLog || log.datetime > existingLog.datetime) {
                    latestLogsByUser.set(log.user, log);
                }
            });

            const result: Log[] = Array.from(latestLogsByUser.values());
            // 数据库操作
            try {
                const db = drizzle(env.DB);
                for (const log of result) {
                    // 查找对应的账号
                    const accounts = await db.select()
                        .from(accountsTable)
                        .where(eq(accountsTable.email, log.user)).limit(1);

                    if (accounts.length > 0) {
                        if (log.content.includes('锁定') || log.content.includes('手机验证')) {
                            if (accounts[0].lock === 0) {
                                let lock = 0;
                                if (log.content.includes('锁定')) {
                                    lock = 1;
                                }
                                else if (log.content.includes('手机验证')) {
                                    lock = 2;
                                }
                                // 设置当前账号为锁定状态
                                await db.update(accountsTable)
                                    .set({ lock: lock })
                                    .where(eq(accountsTable.email, log.user));

                                //暂时不补被锁定的账号
                                continue;
                                // 查找一个可用的空闲账号
                                const freeAccounts = await db.select()
                                    .from(accountsTable)
                                    .where(and(
                                        eq(accountsTable.server, "")
                                    ))
                                    .limit(10)

                                // 遍历空闲账号，尝试更新直到成功
                                for (const freeAccount of freeAccounts) {
                                    // 使用条件更新，确保server字段仍然为空
                                    const result = await db.update(accountsTable)
                                        .set({ server: accounts[0].server })
                                        .where(and(
                                            eq(accountsTable.email, freeAccount.email),
                                            eq(accountsTable.server, "") // 确保server仍为空，防止被其他事务修改
                                        ));

                                    // 检查是否成功更新（受影响行数）
                                    if (result.meta.changes > 0) {
                                        // 成功更新一个账号，退出循环
                                        break;
                                    }
                                    // 如果更新失败，继续尝试下一个账号
                                }
                            }
                        }
                        else if(log.content.includes('用户信息') )
                        {
                            //用户信息:${score}|${pcSearchPointProgress}|${mobileSearchPointProgress}|${pcSearchCount}|${mobileSearchCount}`
                        
                            const values = log.content.split(':')[1].split('|').map(v => parseInt(v) || 0);
                            const [score, pcSearchPointProgress, mobileSearchPointProgress, pcSearchCount, mobileSearchCount] = values;

                            const executions = userExecutions.get(log.user) || 0;
                            const updateData: any = {
                                score,
                                pcSearchPointProgress,
                                mobileSearchPointProgress,
                                updateDatetime: log.datetime,
                                executions
                            };

                            const today = moment().tz('Asia/Shanghai').format('YYYY-MM-DD');
                            // Extract date portion from existing datetime for comparison
                            const existingDate = accounts[0].updateDatetime;
                            if (!existingDate || !existingDate.startsWith(today)) {
                                // Different day - directly set the counts
                                updateData.pcSearchCount = pcSearchCount;
                                updateData.mobileSearchCount = mobileSearchCount;
                            } else {
                                // Same day - accumulate the counts
                                updateData.pcSearchCount = (accounts[0].pcSearchCount || 0) + pcSearchCount;
                                updateData.mobileSearchCount = (accounts[0].mobileSearchCount || 0) + mobileSearchCount;
                            }

                            await db.update(accountsTable)
                                .set(updateData)
                                .where(eq(accountsTable.email, log.user));
                        }
                    }
                }
            }
            catch (e) {
                console.error(e);
            }
            // 转换回数组
            return result;
        }
    }
]
