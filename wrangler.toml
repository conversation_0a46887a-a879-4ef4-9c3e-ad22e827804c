# Generated by <PERSON><PERSON><PERSON> on Mon Jan 27 2025 14:11:26 GMT+0800 (中国标准时间)
name = "seedlog"
compatibility_date = "2025-01-27"
compatibility_flags = [ "nodejs_compat_v2" ]


# Pages 配置
pages_build_output_dir = "dist"


# 开发环境
vars = {}  # 不要在这里写入实际值 写在.dev.vars
kv_namespaces = [
  { binding = "KV", id = "seedlog-local" }
]

[[ d1_databases ]]
binding = "DB"
database_name = "seedlog"
database_id = "54e16795-ed28-41ac-96c8-211a2a431a29"
migrations_dir = "drizzle"
preview_database_id ="seedlog"  # 本地数据库名生成的规则..如果有该值则使用该值.如果没有则使用database_id

[ai]
binding = "AI"

# 预览环境配置
[env.preview]
vars = {}  # 不要在这里写入实际值 写在.dev.vars
kv_namespaces = [
  { binding = "KV", id = "d49cb3b21e9948fb8e6debbe255473c4" }
]

# 生产环境配置
[env.production]
vars = {}  # 不要在这里写入实际值 设置在网站上 
kv_namespaces = [
  { binding = "KV", id = "d49cb3b21e9948fb8e6debbe255473c4" }
]

[env.production.ai]
binding = "AI"

[[env.production.d1_databases]]
binding = "DB"
database_name = "seedlog"
database_id = "54e16795-ed28-41ac-96c8-211a2a431a29"
migrations_dir = "drizzle"