<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, computed } from 'vue';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import { accountApi } from '../services/accountApi';
import { useServerStore } from '../stores/serverStorage';
import MonacoEditor from '../components/MonacoEditor.vue';

const store = useServerStore();
const servers = computed(() => [
    ...store.servers,
    { id: '', name: '未分配账号' },
    { id: '-1', name: '锁定账号' },
    { id: '-2', name: '验证手机' },
]);

const selectedServerId = ref<string>("");
const accountsText = ref<string>('');
const loading = ref(false);
const showDiff = ref(false);
const originalText = ref('');
const buttonText = ref('保存账号');

const fetchAccounts = async () => {
    try {
        loading.value = true;
        buttonText.value = '加载中...';
        accountsText.value = "";
        let data;
        if (selectedServerId.value === '-1') {
            data = await accountApi.locked(1);
        }
        else if (selectedServerId.value === '-2') {
            data = await accountApi.locked(2);
        }
        else {
            data = await accountApi.get(selectedServerId.value);
        }
        const formattedData = JSON.stringify(data, null, 2);
        accountsText.value = formattedData;
        originalText.value = formattedData;
    } catch (error) {
        MessagePlugin.error('获取账号数据失败');
    } finally {
        loading.value = false;
        buttonText.value = '保存账号';
    }
};

watch(selectedServerId, () => {
    fetchAccounts();
});


const handleSave = async () => {
    try {
        if (selectedServerId.value === '-1') {
            MessagePlugin.error('锁定账号无法保存');
            return;
        }
        if (accountsText.value === '') {
            MessagePlugin.error('账号数据不能为空');
            return;
        }
        if (originalText.value === accountsText.value) {
            MessagePlugin.success('数据未修改');
            return;
        }

        // 验证JSON格式
        try {
            JSON.parse(accountsText.value);
        } catch (error) {
            MessagePlugin.error('JSON格式错误');
            return;
        }

        // 显示确认对话框
        const dialog = DialogPlugin.confirm({
            header: '确认保存',
            body: '确定要保存账号数据吗？',
            confirmBtn: '确定',
            cancelBtn: '取消',
            onConfirm: async () => {
                dialog.hide();
                loading.value = true;
                buttonText.value = '保存中';
                try {
                    const accounts = JSON.parse(accountsText.value);
                    const result = await accountApi.update(selectedServerId.value, accounts);
                    if (result.error) {
                        MessagePlugin.error(`${result.error}`);
                        return;
                    }
                    await fetchAccounts();
                    MessagePlugin.success('保存成功');
                } catch (error) {
                    MessagePlugin.error('保存失败');
                } finally {
                    loading.value = false;
                    buttonText.value = '保存账号';
                }
            }
        });
    } catch (error) {
        MessagePlugin.error('操作失败');
    }
};

// 定义按键事件处理函数
const handleKeyDown = (e: KeyboardEvent) => {
    if ((e.ctrlKey || e.metaKey) && e.key.toLowerCase() === 's') {
        e.preventDefault();
        handleSave();
    }
};


// 在显示差异按钮点击处理中添加
const toggleDiff = () => {
    showDiff.value = !showDiff.value;

    if (showDiff.value) {
        // 进入差异模式时，确保有原始文本作为比较
        if (originalText.value === '') {
            originalText.value = accountsText.value;
        }
    }
};
// 在 AccountView.vue 中添加
watch(showDiff, (newVal) => {
    if (newVal && originalText.value === accountsText.value) {
        // 如果开启差异模式但两个文本相同，可以考虑提示用户
        MessagePlugin.info('当前没有差异可以显示');
    }
}, { immediate: true });

onMounted(async () => {
    // 注册全局按键监听
    window.addEventListener('keydown', handleKeyDown);
    await store.fetchServers();
    await fetchAccounts();
});

onUnmounted(() => {
    // 注销全局按键监听
    window.removeEventListener('keydown', handleKeyDown);
});

const handleDelete = async () => {
    try {
        if (accountsText.value === '') {
            MessagePlugin.error('账号数据不能为空');
            return;
        }

        let accountsToDelete: any[] = [];
        try {
            accountsToDelete = JSON.parse(accountsText.value);
            if (!Array.isArray(accountsToDelete)) {
                throw new Error('数据必须是数组格式');
            }
            // 验证每个对象都有 email 属性
            if (accountsToDelete.some(acc => typeof acc.email !== 'string')) {
                 throw new Error('数组中的每个对象都必须包含一个 email 字符串属性');
            }
        } catch (error: any) {
            MessagePlugin.error(`JSON 格式错误或数据结构无效: ${error.message}`);
            return;
        }

        if (accountsToDelete.length === 0) {
             MessagePlugin.info('没有可删除的账号');
             return;
        }

        const emailsToDelete = accountsToDelete.map(acc => acc.email);

        // 显示确认对话框
        const dialog = DialogPlugin.confirm({
            header: '确认删除',
            body: `确定要删除这 ${emailsToDelete.length} 个账号吗？此操作不可恢复！`,
            confirmBtn: { content: '确定删除', theme: 'danger' },
            cancelBtn: '取消',
            onConfirm: async () => {
                dialog.hide();
                loading.value = true;
                // 可以考虑修改按钮文本，例如 buttonText.value = '删除中...'
                try {
                    const result = await accountApi.delete(emailsToDelete);
                    if (result.error) {
                        MessagePlugin.error(`删除失败: ${result.error}`);
                        return;
                    }
                    MessagePlugin.success(`成功删除 ${result.message.match(/\d+/)?.[0] ?? emailsToDelete.length} 个账号`);
                    await fetchAccounts(); // 重新加载数据
                } catch (error: any) {
                    MessagePlugin.error(`删除操作失败: ${error.message || '未知错误'}`);
                } finally {
                    loading.value = false;
                    // 恢复按钮文本 buttonText.value = '保存账号'; (如果修改了)
                }
            }
        });
    } catch (error: any) {
        MessagePlugin.error(`操作失败: ${error.message || '未知错误'}`);
    }
};
</script>

<template>
    <div class="account-container h-full p-2 md:p-5">
        <t-card bordered class="h-full">
            <template #content>
                <div class=" flex flex-col h-full">
                    <div class="flex justify-between items-center mb-4 gap-4">
                        <t-select v-model="selectedServerId">
                            <t-option v-for="s in servers" :key="s.id" :value="s.id" :label="s.name" />
                        </t-select>
                        <t-button variant="outline" @click="toggleDiff">
                            {{ showDiff ? '隐藏对比' : '显示对比' }}
                        </t-button>
                        <t-button theme="primary" @click="handleSave" :loading="loading"
                            v-if="!selectedServerId.startsWith('-')">
                            {{ buttonText }}
                        </t-button>
                        <t-button theme="danger" @click="handleDelete" :loading="loading" v-else>
                            删除账号
                        </t-button>
                    </div>

                    <div class="editor-container flex-1">
                        <MonacoEditor v-model:value="accountsText" :original-value="showDiff ? originalText : undefined"
                            language="json" :options="{ tabSize: 2 }" />
                    </div>
                </div>
            </template>
        </t-card>
    </div>
</template>

<style scoped>
.account-container {
    width: 100%;
}

:deep(.t-card__body) {
    height: 100%;
}

.editor-container {
    border: 1px solid var(--td-component-border);
}
</style>
