import { EventContext } from '@cloudflare/workers-types'
import { Env, SystemSettings } from '../types'
import { authMiddleware } from '../utils/auth';
import { addCorsHeaders } from '../utils/cors';
import { drizzle } from 'drizzle-orm/d1';
import { settingsTable } from '../db/schema';
import { eq } from 'drizzle-orm';

export const onRequest = async (context: EventContext<Env, string, Record<string, unknown>>): Promise<Response> => {
    const request = context.request;
    const env = context.env as Env;
    const db = drizzle(env.DB);

    // 验证权限
    const authResponse = await authMiddleware(request, env);
    if (authResponse) {
        return addCorsHeaders(authResponse);
    }

    try {
        const newSettings = await request.json() as SystemSettings;
        const existingSettings = await db.select().from(settingsTable).all();
        
        // 保存新的设置
        for (const [key, value] of Object.entries(newSettings)) {
            const exists = existingSettings.find(s => s.key === key);
            if (exists) {
                await db.update(settingsTable)
                    .set({ value: JSON.stringify(value) })
                    .where(eq(settingsTable.key, key))
                    .run();
            } else {
                await db.insert(settingsTable)
                    .values({ 
                        key, 
                        value: JSON.stringify(value)
                    })
                    .run();
            }
        }

        // 删除不再存在的设置
        const existingKeys = existingSettings.map(s => s.key);
        const newKeys = Object.keys(newSettings);
        const keysToDelete = existingKeys.filter(key => !newKeys.includes(key));
        
        for (const key of keysToDelete) {
            await db.delete(settingsTable)
                .where(eq(settingsTable.key, key))
                .run();
        }

        return addCorsHeaders(new Response(JSON.stringify({ message: 'Settings saved successfully' }), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
        }));
    } catch (error) {
        return addCorsHeaders(new Response(JSON.stringify({ error: 'Failed to save settings' }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        }));
    }
}