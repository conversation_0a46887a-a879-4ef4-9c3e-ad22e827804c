{"version": "6", "dialect": "sqlite", "id": "1178ee24-bdc8-4f54-831f-dd910831e8dd", "prevId": "0ab646c8-e020-4661-ae17-86eb6489c504", "tables": {"accounts": {"name": "accounts", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "proofEmail": {"name": "proofEmail", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "server": {"name": "server", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "score": {"name": "score", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "weight": {"name": "weight", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "disabled": {"name": "disabled", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "lock": {"name": "lock", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}}, "indexes": {"server_idx": {"name": "server_idx", "columns": ["server"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "logs": {"name": "logs", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "server": {"name": "server", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "service": {"name": "service", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "datetime": {"name": "datetime", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "logs": {"name": "logs", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"logs_compound_idx": {"name": "logs_compound_idx", "columns": ["server", "service", "datetime"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "servers": {"name": "servers", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "disabled": {"name": "disabled", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}}, "indexes": {"server_name_idx": {"name": "server_name_idx", "columns": ["name"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "settings": {"name": "settings", "columns": {"key": {"name": "key", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}