import { EventContext } from '@cloudflare/workers-types'
import { Env } from '../types'
import { authMiddleware } from '../utils/auth';
import { addCorsHeaders } from '../utils/cors';
import { getSystemSettings } from '../services/settingService';

export const onRequest = async (context: EventContext<Env, string, Record<string, unknown>>): Promise<Response> => {
    const request = context.request;
    const env = context.env as Env;

    // 验证权限
    const authResponse = await authMiddleware(request, env);
    if (authResponse) {
        return addCorsHeaders(authResponse);
    }

    try {
        const settings = await getSystemSettings(env);
        return addCorsHeaders(new Response(JSON.stringify(settings), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
        }));
    } catch (error) {
        return addCorsHeaders(new Response(JSON.stringify({ error: 'Failed to fetch settings' }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        }));
    }
}