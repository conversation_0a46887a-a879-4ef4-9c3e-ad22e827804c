import { EventContext } from '@cloudflare/workers-types'
import { Env } from '../types'
import { authApiToken, authMiddleware } from '../utils/auth';
import { handleOptions, addCorsHeaders } from '../utils/cors';
import { drizzle } from 'drizzle-orm/d1';
import { serversTable } from '../db/schema';

export const onRequest = async (context: EventContext<Env, string, Record<string, unknown>>): Promise<Response> => {
    const request = context.request;
    const env = context.env as Env;
    const db = drizzle(env.DB);

    // 验证权限
    const authResponse = await authMiddleware(request, env);
    const apiResponse = await authApiToken(request, env);
    if (authResponse && apiResponse) {
        return addCorsHeaders(authResponse);
    }

    try {
        const servers = await db.select().from(serversTable);
        // Parse config field for each server
        const serversWithParsedConfig = servers.map(server => ({
            ...server,
            config: JSON.parse(server.config || '{}')
        }));
        
        return addCorsHeaders(new Response(JSON.stringify(serversWithParsedConfig), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
        }));
    } catch (error) {
        return addCorsHeaders(new Response(JSON.stringify({
            error: error instanceof Error ? error.message : 'Failed to retrieve servers'
        }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        }));
    }
}
