{"version": "6", "dialect": "sqlite", "id": "92bffe49-59e0-4871-ac65-7ab39d5936b5", "prevId": "0ed056a6-98bb-42ca-a36a-944aa99b5570", "tables": {"accounts": {"name": "accounts", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "proofEmail": {"name": "proofEmail", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "server": {"name": "server", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "score": {"name": "score", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "weight": {"name": "weight", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "disabled": {"name": "disabled", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "lock": {"name": "lock", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "pcSearchPointProgress": {"name": "pcSearchPointProgress", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "mobileSearchPointProgress": {"name": "mobileSearchPointProgress", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "pcSearchCount": {"name": "pcSearchCount", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "mobileSearchCount": {"name": "mobileSearchCount", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "executions": {"name": "executions", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "updateDatetime": {"name": "updateDatetime", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "''"}, "maxSearchPerRequest": {"name": "maxSearchPerRequest", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": -1}, "maxDailySearchLimit": {"name": "maxDailySearchLimit", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": -1}, "maxReadPerRequest": {"name": "maxReadPerRequest", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": -1}, "maxDailyReadLimit": {"name": "maxDailyReadLimit", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": -1}, "ignoreDistributionCycleDays": {"name": "ignoreDistributionCycleDays", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}}, "indexes": {"server_idx": {"name": "server_idx", "columns": ["server"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "logs": {"name": "logs", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "server": {"name": "server", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "service": {"name": "service", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "datetime": {"name": "datetime", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "logs": {"name": "logs", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"logs_compound_idx": {"name": "logs_compound_idx", "columns": ["server", "service", "datetime"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "servers": {"name": "servers", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "disabled": {"name": "disabled", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "config": {"name": "config", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'{}'"}}, "indexes": {"server_name_idx": {"name": "server_name_idx", "columns": ["name"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "settings": {"name": "settings", "columns": {"key": {"name": "key", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}