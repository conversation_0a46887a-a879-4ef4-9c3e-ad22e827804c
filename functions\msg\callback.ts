import { EventContext } from '@cloudflare/workers-types'
import { Env,  LoginCredentials, SystemSettings } from '../types'
import { generateToken } from '../utils/jwt';
import { authApiToken, authMiddleware } from '../utils/auth';
import { handleOptions, addCorsHeaders } from '../utils/cors';
import { AESCipher, handleAuth, EventMessage } from './utils';


//飞书的回调订阅,飞书可以通过设置回调订阅地址,将消息推送到此处.然后直接返回数据就可以进行消息推送等操作
//https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/event-subscription-guide/callback-subscription/callback-overview
// 回调处理的内容很少: 卡片回传交互 查看自定义名片页卡片 拉取链接预览数据 都在配置页面的下面
export const onRequest = async (context: EventContext<Env, string, Record<string, unknown>>): Promise<Response> => {
    const request = context.request;

    const env = context.env as Env;
    const kv = env.KV || KV;

    const settings = JSON.parse(await kv.get('settings') as string) as SystemSettings;
    const data = await handleAuth(request, settings.feishu.verification_token, settings.feishu.encrypt_key);
    if (data instanceof Response) {
        return data;
    }

    let body = data as EventMessage;



    return new Response(JSON.stringify({ message: 'Hello, World!' }));
}