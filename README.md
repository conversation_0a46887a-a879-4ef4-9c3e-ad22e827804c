# seedlog


# kv
wrangler的本地的kv会存储到.wrangler/state/v3/kv/miniflare-KVNamespaceObject目录下,是个sqlite


# d1 
wrangler的本地的d1会存储到.wrangler/state/v3/d1/miniflare-D1DatabaseObject目录下,是个sqlite
d1的迁移又两种方式:

1. 使用命令 `wrangler d1 migrations` https://developers.cloudflare.com/d1/reference/migrations/
需要在配置文件种定义migrations_dir(从哪里迁移)和database_name(迁移哪一个)和database_id(迁移到哪里)

```
# wrangler d1 migrations apply seedlog --local

# 开发环境
vars = {}  # 不要在这里写入实际值 写在.dev.vars

[[ d1_databases ]]
binding = "DB"
database_name = "seedlog"
database_id = "54e16795-ed28-41ac-96c8-211a2a431a29"
migrations_dir = "drizzle"
preview_database_id ="seedlog"  # 本地数据库名生成的规则..如果有该值则使用该值.如果没有则使用database_id


# wrangler d1 migrations apply seedlog --remote


# 生产环境配置
[env.production]
vars = {}  # 不要在这里写入实际值 设置在网站上 

[[env.production.d1_databases]]
binding = "DB"
migrations_dir = "drizzle"
database_name = "seedlog"
database_id = "c529e455-d749-458c-9e8b-f0611bec7572"
```

2. 使用`npx drizzle-kit migrate`


```
# 本地修改可以使用push..测试完后再使用migrate到本地或者远程
# npx drizzle-kit push --config=drizzle-dev.drizzle.config.ts
# npx drizzle-kit migrate --config=drizzle-dev.drizzle.config.ts

import 'dotenv/config';
import { Config, defineConfig } from 'drizzle-kit';

export default defineConfig({
  out: './drizzle',
  schema: './functions/db/schema.ts',
  dialect: 'sqlite',
  dbCredentials: {
    url: process.env.DB_FILE_NAME!,
  },
} satisfies Config);


# npx drizzle-kit migrate --config=drizzle-prod.drizzle.config.ts
# 使用了Cloudflare D1 HTTP API进行迁移
# https://developers.cloudflare.com/api/resources/d1/subresources/database/
import 'dotenv/config';
import { defineConfig } from 'drizzle-kit';
export default defineConfig({
  out: './drizzle',
  schema: './src/db/schema.ts',
  dialect: 'sqlite',
  driver: '',
  dbCredentials: {
    accountId: process.env.CLOUDFLARE_ACCOUNT_ID!,
    databaseId: process.env.CLOUDFLARE_DATABASE_ID!,
    token: process.env.CLOUDFLARE_D1_TOKEN!,
  },
});

```