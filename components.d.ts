/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    MonacoEditor: typeof import('./src/components/MonacoEditor.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TAside: typeof import('tdesign-vue-next')['Aside']
    TButton: typeof import('tdesign-vue-next')['Button']
    TCard: typeof import('tdesign-vue-next')['Card']
    TContent: typeof import('tdesign-vue-next')['Content']
    TDatePicker: typeof import('tdesign-vue-next')['DatePicker']
    TDialog: typeof import('tdesign-vue-next')['Dialog']
    TDivider: typeof import('tdesign-vue-next')['Divider']
    TDrawer: typeof import('tdesign-vue-next')['Drawer']
    TFooter: typeof import('tdesign-vue-next')['Footer']
    TForm: typeof import('tdesign-vue-next')['Form']
    TFormItem: typeof import('tdesign-vue-next')['FormItem']
    THeader: typeof import('tdesign-vue-next')['Header']
    TIcon: typeof import('tdesign-vue-next')['Icon']
    TInput: typeof import('tdesign-vue-next')['Input']
    TLayout: typeof import('tdesign-vue-next')['Layout']
    TMenu: typeof import('tdesign-vue-next')['Menu']
    TMenuItem: typeof import('tdesign-vue-next')['MenuItem']
    TOption: typeof import('tdesign-vue-next')['Option']
    TPagination: typeof import('tdesign-vue-next')['Pagination']
    TSelect: typeof import('tdesign-vue-next')['Select']
    TTable: typeof import('tdesign-vue-next')['Table']
  }
}
