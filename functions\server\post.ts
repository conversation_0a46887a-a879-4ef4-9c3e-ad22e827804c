import { EventContext } from '@cloudflare/workers-types'
import { Env } from '../types'
import { authApiToken, authMiddleware } from '../utils/auth';
import { handleOptions, addCorsHeaders } from '../utils/cors';
import { drizzle } from 'drizzle-orm/d1';
import { serversTable } from '../db/schema';
import { eq } from 'drizzle-orm';

export const onRequest = async (context: EventContext<Env, string, Record<string, unknown>>): Promise<Response> => {
    const request = context.request;
    const env = context.env as Env;
    const db = drizzle(env.DB);

    // 验证权限
    const authResponse = await authMiddleware(request, env);
    const apiResponse = await authApiToken(request, env);
    if (authResponse && apiResponse) {
        return addCorsHeaders(authResponse);
    }

    try {
        const newServers = await request.json() as Array<{
            id: string;
            name: string;
            disabled: number;
            config?: Record<string, any>;
        }>;

        // 获取现有服务器列表
        const existingServers = await db.select().from(serversTable);
        const existingMap = new Map(existingServers.map(s => [s.id, s]));
        
        for (const server of newServers) {
            const existing = existingMap.get(server.id);
            
            if (!existing) {
                // 新增服务器
                await db.insert(serversTable).values({
                    id: server.id,
                    name: server.name,
                    disabled: server.disabled,
                    config: JSON.stringify(server.config || {})
                });
            } else if (
                existing.name !== server.name || 
                existing.disabled !== server.disabled ||
                existing.config !== JSON.stringify(server.config || {})
            ) {
                // 更新服务器
                await db.update(serversTable)
                    .set({
                        name: server.name,
                        disabled: server.disabled,
                        config: JSON.stringify(server.config || {})
                    })
                    .where(eq(serversTable.id, server.id));
            }
            // 从Map中删除已处理的服务器
            existingMap.delete(server.id);
        }

        // 删除不在新列表中的服务器
        for (const [id] of existingMap) {
            await db.delete(serversTable)
                .where(eq(serversTable.id, id));
        }

        return addCorsHeaders(new Response(JSON.stringify({ success: true }), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
        }));
    } catch (error) {
        return addCorsHeaders(new Response(JSON.stringify({
            error: error instanceof Error ? error.message : 'Failed to update servers'
        }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        }));
    }
}
