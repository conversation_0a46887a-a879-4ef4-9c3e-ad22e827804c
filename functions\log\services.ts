import type { Env } from '../types';
import { drizzle } from 'drizzle-orm/d1';
import { accountsTable } from '../db/schema';
import { eq, and } from 'drizzle-orm';
import { authMiddleware } from '../utils/auth';
import { addCorsHeaders } from '../utils/cors';
import { services } from './logServices';

export const onRequest = async (context: EventContext<Env, string, Record<string, unknown>>): Promise<Response> => {
    const request = context.request;
    const env = context.env as Env;
    // 验证权限
    const authResponse = await authMiddleware(request, env);
    if (authResponse) {
        return addCorsHeaders(authResponse);
    }
    return new Response(JSON.stringify(services.map(x => {
        return {
            id: x.id,
            name: x.name
        }
    })), {
        headers: { 'Content-Type': 'application/json' }
    });
}