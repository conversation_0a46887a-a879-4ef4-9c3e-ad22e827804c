import { defineStore } from 'pinia'
import { serverApi } from '../services/serverApi'

export const useServerStore = defineStore('server', {
  state: () => ({
    servers: [] as any[],
    initialized: false
  }),
  
  actions: {
    async fetchServers(force = false) {
      if (!force && this.initialized) return;
      try {
        const data = await serverApi.get()
        this.servers = data || []
        this.initialized = true
      } catch (error) {
        console.error('Failed to fetch servers:', error)
      }
    },
    
    updateServers(newServers:any[]) {
      this.servers = newServers
    }
  }
})