import { EventContext } from '@cloudflare/workers-types';
import { Env } from '../types';
import moment from 'moment-timezone';
import { authApiToken, authMiddleware } from '../utils/auth';
import { handleOptions, addCorsHeaders } from '../utils/cors';
import { drizzle } from 'drizzle-orm/d1';
import { accountsTable, serversTable } from '../db/schema';
import { eq, and } from 'drizzle-orm';

export const onRequest = async (context: EventContext<Env, string, Record<string, unknown>>): Promise<Response> => {
    const request = context.request;
    const env = context.env as Env;
    const db = drizzle(env.DB);

    // 验证权限
    const authResponse = await authMiddleware(request, env);
    const apiResponse = await authApiToken(request, env);
    if (authResponse && apiResponse) {
        return addCorsHeaders(authResponse);
    }

    try {
        const url = new URL(request.url);
        const server = url.searchParams.get('server') || "";
        const ignoreDisabled = url.searchParams.get('ignoreDisabled') === '1';

        // 检查服务器状态
        const serverInfo = await db.select().from(serversTable).where(eq(serversTable.id, server));
        if (serverInfo.length > 0 && serverInfo[0].disabled === 1 && !ignoreDisabled) {
            return addCorsHeaders(new Response(JSON.stringify([]), {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            }));
        }

        const lock = parseInt(url.searchParams.get('lock') || "0");
        let accounts = await db.select().from(accountsTable).where(and(
            eq(accountsTable.server, server),
            eq(accountsTable.lock, lock),
        ));

        // Check if updateDatetime is today
        const today = moment().tz('Asia/Shanghai').format('YYYY-MM-DD');
        accounts = accounts.map(account => {
            if (!account.updateDatetime || !account.updateDatetime.startsWith(today)) {
                return { ...account, executions: 0, pcSearchCount: 0, mobileSearchCount: 0, pcSearchPointProgress: 0, mobileSearchPointProgress: 0 };
            }
            return account;
        });

        return addCorsHeaders(new Response(JSON.stringify(accounts), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
        }));
    } catch (error) {
        return addCorsHeaders(new Response(JSON.stringify({
            error: error instanceof Error ? error.message : 'Failed to retrieve accounts'
        }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        }));
    }
}
