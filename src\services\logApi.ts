import { API_BASE_URL, getHeaders, handleResponse } from './util';

export const logApi = {
    async listLog(
        server: string, 
        service: string, 
        datetime: string
    ) {
        const params = new URLSearchParams({
            server,
            service,
            datetime: datetime
        });
        const response = await fetch(`/log/list?${params}`,       {
            headers: getHeaders()
        });
        return response.json();
    },

    async listServices() {
        const response = await fetch(`/log/services`,       {
            headers: getHeaders()
        });
        return response.json();
    }

}